{"version": 3, "file": "stringify.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["stringify.ts"], "names": [], "mappings": ";;;;;AAkBA,oCAKC;AASD,oCAOC;AAUD,0BAMC;AAUD,kCAOC;AAUD,8BAOC;AAzFD,yCAOoB;AACpB,kEAAkE;AAClE,iDAA6C;AAE7C;;;;;;GAMG;AACH,SAAgB,YAAY,CACxB,IAAkC,EAClC,OAA8B;IAE9B,OAAO,IAAA,wBAAU,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACrC,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,YAAY,CACxB,IAAa,EACb,OAA8B;IAE9B,OAAO,IAAA,wBAAW,EAAC,IAAI,CAAC;QACpB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,EAA3B,CAA2B,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;QACnE,CAAC,CAAC,EAAE,CAAC;AACb,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,OAAO,CAAC,IAAyB;IAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3D,IAAI,IAAA,kBAAK,EAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3E,IAAI,IAAA,oBAAO,EAAC,IAAI,CAAC;QAAE,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,IAAI,IAAA,mBAAM,EAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnC,OAAO,EAAE,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,WAAW,CAAC,IAAyB;IACjD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC/D,IAAI,IAAA,wBAAW,EAAC,IAAI,CAAC,IAAI,CAAC,IAAA,sBAAS,EAAC,IAAI,CAAC,EAAE,CAAC;QACxC,OAAO,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IACD,IAAI,IAAA,mBAAM,EAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnC,OAAO,EAAE,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,SAAS,CAAC,IAAyB;IAC/C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7D,IAAI,IAAA,wBAAW,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,4BAAW,CAAC,GAAG,IAAI,IAAA,oBAAO,EAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QACxE,OAAO,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IACD,IAAI,IAAA,mBAAM,EAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnC,OAAO,EAAE,CAAC;AACd,CAAC"}