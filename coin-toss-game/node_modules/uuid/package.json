{"name": "uuid", "version": "7.0.3", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "dist/index.js", "module": "dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "files": ["CHANGELOG.md", "CONTRIBUTING.md", "LICENSE.md", "README.md", "deprecate.js", "dist", "v1.js", "v3.js", "v4.js", "v5.js"], "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.8.7", "@babel/preset-env": "7.8.7", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-node-resolve": "7.1.1", "@wdio/browserstack-service": "5.18.7", "@wdio/cli": "5.18.7", "@wdio/jasmine-framework": "5.18.6", "@wdio/local-runner": "5.18.7", "@wdio/spec-reporter": "5.18.7", "@wdio/static-server-service": "5.16.10", "@wdio/sync": "5.18.7", "babel-eslint": "10.1.0", "babel-plugin-add-module-exports": "1.0.2", "bundlewatch": "0.2.6", "eslint": "6.8.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "husky": "3.0.9", "jest": "25.1.0", "lint-staged": "10.0.8", "npm-run-all": "4.1.5", "prettier": "1.19.1", "rollup": "1.32.0", "rollup-plugin-terser": "5.2.0", "runmd": "1.3.2", "standard-version": "7.1.0"}, "scripts": {"examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && bundlewatch --config bundlewatch.config.json )", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "build": "./scripts/build.sh", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md", "postcommit": "npm run build"}}}