{"name": "@expo/spawn-async", "version": "1.7.2", "description": "A Promise-based interface into processes created by child_process.spawn", "main": "./build/spawnAsync.js", "types": "./build/spawnAsync.d.ts", "files": ["build", "!build/**/__tests__"], "engines": {"node": ">=12"}, "scripts": {"build": "tsc", "clean": "rm -rf build", "prepare": "yarn clean && yarn build", "start": "tsc --watch", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/expo/spawn-async.git"}, "keywords": ["spawn", "child_process", "async", "promise", "process"], "author": "Expo", "license": "MIT", "bugs": {"url": "https://github.com/expo/spawn-async/issues"}, "homepage": "https://github.com/expo/spawn-async#readme", "jest": {"preset": "ts-jest", "rootDir": "src"}, "dependencies": {"cross-spawn": "^7.0.3"}, "devDependencies": {"@types/cross-spawn": "^6.0.2", "@types/jest": "^29.5.0", "@types/node": "^18.15.3", "jest": "^29.5.0", "ts-jest": "^29.0.5", "typescript": "^5.0.2"}}