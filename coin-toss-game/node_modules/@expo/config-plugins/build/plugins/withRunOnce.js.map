{"version": 3, "file": "withRunOnce.js", "names": ["_history", "data", "require", "withRunOnce", "config", "plugin", "name", "version", "getHistoryItem", "addHistoryItem", "exports", "createRunOncePlugin", "props"], "sources": ["../../src/plugins/withRunOnce.ts"], "sourcesContent": ["import { ConfigPlugin } from '../Plugin.types';\nimport { addHistoryItem, getHistoryItem, PluginHistoryItem } from '../utils/history';\n\n/**\n * Prevents the same plugin from being run twice.\n * Used for migrating from unversioned expo config plugins to versioned plugins.\n *\n * @param config\n * @param name\n */\nexport const withRunOnce: ConfigPlugin<{\n  plugin: ConfigPlugin<void>;\n  name: PluginHistoryItem['name'];\n  version?: PluginHistoryItem['version'];\n}> = (config, { plugin, name, version }) => {\n  // Detect if a plugin has already been run on this config.\n  if (getHistoryItem(config, name)) {\n    return config;\n  }\n\n  // Push the history item so duplicates cannot be run.\n  config = addHistoryItem(config, { name, version });\n\n  return plugin(config);\n};\n\n/**\n * Helper method for creating mods from existing config functions.\n *\n * @param action\n */\nexport function createRunOncePlugin<T>(\n  plugin: ConfigPlugin<T>,\n  name: string,\n  version?: string\n): ConfigPlugin<T> {\n  return (config, props) => {\n    return withRunOnce(config, { plugin: (config) => plugin(config, props), name, version });\n  };\n}\n"], "mappings": ";;;;;;;AACA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAME,WAIX,GAAGA,CAACC,MAAM,EAAE;EAAEC,MAAM;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAC1C;EACA,IAAI,IAAAC,yBAAc,EAACJ,MAAM,EAAEE,IAAI,CAAC,EAAE;IAChC,OAAOF,MAAM;EACf;;EAEA;EACAA,MAAM,GAAG,IAAAK,yBAAc,EAACL,MAAM,EAAE;IAAEE,IAAI;IAAEC;EAAQ,CAAC,CAAC;EAElD,OAAOF,MAAM,CAACD,MAAM,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJAM,OAAA,CAAAP,WAAA,GAAAA,WAAA;AAKO,SAASQ,mBAAmBA,CACjCN,MAAuB,EACvBC,IAAY,EACZC,OAAgB,EACC;EACjB,OAAO,CAACH,MAAM,EAAEQ,KAAK,KAAK;IACxB,OAAOT,WAAW,CAACC,MAAM,EAAE;MAAEC,MAAM,EAAGD,MAAM,IAAKC,MAAM,CAACD,MAAM,EAAEQ,KAAK,CAAC;MAAEN,IAAI;MAAEC;IAAQ,CAAC,CAAC;EAC1F,CAAC;AACH", "ignoreList": []}