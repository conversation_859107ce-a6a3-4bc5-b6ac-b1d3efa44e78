{"version": 3, "file": "errors.js", "names": ["UnexpectedError", "Error", "name", "constructor", "message", "exports", "PluginError", "isPluginError", "code", "cause"], "sources": ["../../src/utils/errors.ts"], "sourcesContent": ["export class UnexpectedError extends Error {\n  readonly name = 'UnexpectedError';\n\n  constructor(message: string) {\n    super(`${message}\\nReport this issue: https://github.com/expo/expo/issues`);\n  }\n}\n\nexport type PluginErrorCode =\n  | 'INVALID_PLUGIN_TYPE'\n  | 'INVALID_PLUGIN_IMPORT'\n  | 'PLUGIN_NOT_FOUND'\n  | 'CONFLICTING_PROVIDER'\n  | 'INVALID_MOD_ORDER'\n  | 'MISSING_PROVIDER';\n\n/**\n * Based on `JsonFileError` from `@expo/json-file`\n */\nexport class PluginError extends Error {\n  readonly name = 'PluginError';\n  readonly isPluginError = true;\n\n  constructor(\n    message: string,\n    public code: PluginErrorCode,\n    public cause?: Error\n  ) {\n    super(cause ? `${message}\\n└─ Cause: ${cause.name}: ${cause.message}` : message);\n  }\n}\n"], "mappings": ";;;;;;AAAO,MAAMA,eAAe,SAASC,KAAK,CAAC;EAChCC,IAAI,GAAG,iBAAiB;EAEjCC,WAAWA,CAACC,OAAe,EAAE;IAC3B,KAAK,CAAC,GAAGA,OAAO,0DAA0D,CAAC;EAC7E;AACF;AAACC,OAAA,CAAAL,eAAA,GAAAA,eAAA;AAUD;AACA;AACA;AACO,MAAMM,WAAW,SAASL,KAAK,CAAC;EAC5BC,IAAI,GAAG,aAAa;EACpBK,aAAa,GAAG,IAAI;EAE7BJ,WAAWA,CACTC,OAAe,EACRI,IAAqB,EACrBC,KAAa,EACpB;IACA,KAAK,CAACA,KAAK,GAAG,GAAGL,OAAO,eAAeK,KAAK,CAACP,IAAI,KAAKO,KAAK,CAACL,OAAO,EAAE,GAAGA,OAAO,CAAC;IAAC,KAH1EI,IAAqB,GAArBA,IAAqB;IAAA,KACrBC,KAAa,GAAbA,KAAa;EAGtB;AACF;AAACJ,OAAA,CAAAC,WAAA,GAAAA,WAAA", "ignoreList": []}