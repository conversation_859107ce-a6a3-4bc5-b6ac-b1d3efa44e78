{"name": "getenv", "description": "Get and typecast environment variables.", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON>", "<PERSON> <<EMAIL>>", "<PERSON>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "version": "2.0.0", "license": "MIT", "homepage": "https://github.com/ctavan/node-getenv", "repository": {"type": "git", "url": "git://github.com/ctavan/node-getenv.git"}, "main": "index.js", "scripts": {"prettier": "prettier --check .", "unit": "bash -ec 'for F in test/*.js; do echo \"$F\": ; node $F; done;'", "test": "npm run prettier && npm run unit"}, "engines": {"node": ">=6"}, "dependencies": {}, "devDependencies": {"prettier": "^3.5.3"}, "keywords": ["env", "environment", "config", "configuration", "12factor"]}