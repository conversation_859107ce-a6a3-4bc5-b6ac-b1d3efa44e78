{"version": 3, "file": "InterfaceBuilder.js", "names": ["_crypto", "data", "_interopRequireDefault", "require", "_xml2js", "e", "__esModule", "default", "debug", "createConstraint", "firstItem", "firstAttribute", "secondItem", "secondAttribute", "constant", "$", "id", "createConstraintId", "attributes", "crypto", "createHash", "update", "join", "digest", "IMAGE_ID", "CONTAINER_ID", "removeImageFromSplashScreen", "xml", "imageName", "mainView", "document", "scenes", "scene", "objects", "viewController", "view", "removeExisting", "subviews", "imageView", "getAbsoluteConstraints", "for<PERSON>ach", "constraint", "constrainsArray", "constraints", "resources", "image", "imageSection", "existingImageIndex", "findIndex", "name", "splice", "childId", "parentId", "legacy", "applyImageToSplashScreenXML", "contentMode", "backgroundColor", "enableFullScreenImage", "imageWidth", "width", "height", "x", "rect", "y", "userLabel", "clipsSubviews", "userInteractionEnabled", "translatesAutoresizingMaskIntoConstraints", "key", "ensureUniquePush", "push", "color", "colorSection", "namedColorSection", "parseColor", "alpha", "blue", "rgb", "green", "red", "customColorSpace", "colorSpace", "namedColor", "array", "item", "existingItem", "toString", "builder", "Builder", "preserveChildrenOrder", "xmldec", "version", "encoding", "renderOpts", "pretty", "indent", "buildObject", "toObjectAsync", "contents", "<PERSON><PERSON><PERSON>", "parseStringPromise", "value", "toUpperCase", "replace", "length", "console", "error", "process", "exit", "hex", "parseInt", "toPrecision", "exports"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/InterfaceBuilder.ts"], "sourcesContent": ["import crypto from 'crypto';\nimport { Builder, Parser } from 'xml2js';\n\nconst debug = require('debug')(\n  'expo:prebuild-config:expo-splash-screen:ios:InterfaceBuilder'\n) as typeof console.log;\n\nexport type IBBoolean = 'YES' | 'NO' | boolean;\n\nexport type IBItem<\n  H extends Record<string, any>,\n  B extends Record<string, any[]> = { [key: string]: any },\n> = {\n  $: H;\n} & B;\n\nexport type Rect = {\n  key: string;\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n};\n\nexport type IBRect = IBItem<Rect>;\n\nexport type IBAutoresizingMask = IBItem<{\n  /** @example `autoresizingMask` */\n  key: string;\n  flexibleMaxX: IBBoolean;\n  flexibleMaxY: IBBoolean;\n}>;\n\n/** @example `<color key=\"textColor\" systemColor=\"linkColor\"/>` */\nexport type IBColor = IBItem<\n  {\n    /** @example `textColor` */\n    key: string;\n  } & (\n    | /** Custom color */\n    {\n        /** @example `0.86584504117670746` */\n        red: number;\n        /** @example `0.26445041990630447` */\n        green: number;\n        /** @example `0.3248577810203549` */\n        blue: number;\n        /** @example `1` */\n        alpha: number;\n        colorSpace: 'custom' | string;\n        customColorSpace: 'displayP3' | 'sRGB' | string;\n      }\n    /** Built-in color */\n    | {\n        systemColor: 'linkColor' | string;\n      }\n  )\n>;\n\nexport type IBFontDescription = IBItem<{\n  /** @example `fontDescription` */\n  key: string;\n  /** Font size */\n  pointSize: number;\n\n  /** Custom font */\n  name?: 'HelveticaNeue' | string;\n  family?: 'Helvetica Neue' | string;\n\n  /** Built-in font */\n  type?: 'system' | 'boldSystem' | 'UICTFontTextStyleCallout' | 'UICTFontTextStyleBody' | string;\n}>;\n\nexport type ImageContentMode = 'scaleAspectFit' | 'scaleAspectFill';\n\nexport type ConstraintAttribute = 'top' | 'bottom' | 'trailing' | 'leading' | 'centerX' | 'centerY';\n\nexport type IBImageView = IBItem<\n  {\n    id: string;\n    userLabel: string;\n    image: string;\n    clipsSubviews?: IBBoolean;\n    userInteractionEnabled: IBBoolean;\n    contentMode: IBContentMode;\n    horizontalHuggingPriority?: number;\n    verticalHuggingPriority?: number;\n    insetsLayoutMarginsFromSafeArea?: IBBoolean;\n    translatesAutoresizingMaskIntoConstraints?: IBBoolean;\n  },\n  {\n    rect: IBRect[];\n  }\n>;\n\nexport type IBLabel = IBItem<\n  {\n    id: string;\n    /** The main value. */\n    text: string;\n\n    opaque: IBBoolean;\n    fixedFrame: IBBoolean;\n    textAlignment?: IBTextAlignment;\n    lineBreakMode:\n      | 'clip'\n      | 'characterWrap'\n      | 'wordWrap'\n      | 'headTruncation'\n      | 'middleTruncation'\n      | 'tailTruncation';\n    baselineAdjustment?: 'none' | 'alignBaselines';\n    adjustsFontSizeToFit: IBBoolean;\n    userInteractionEnabled: IBBoolean;\n    contentMode: IBContentMode;\n    horizontalHuggingPriority: number;\n    verticalHuggingPriority: number;\n    translatesAutoresizingMaskIntoConstraints?: IBBoolean;\n  },\n  {\n    /** @example `<rect key=\"frame\" x=\"175\" y=\"670\" width=\"35\" height=\"17\"/>` */\n    rect: IBRect[];\n    /** @example `<autoresizingMask key=\"autoresizingMask\" flexibleMaxX=\"YES\" flexibleMaxY=\"YES\"/>` */\n    autoresizingMask?: IBAutoresizingMask[];\n    /** @example `<fontDescription key=\"fontDescription\" type=\"system\" pointSize=\"19\"/>` */\n    fontDescription?: IBFontDescription[];\n    /** @example `<color key=\"textColor\" red=\"0.0\" green=\"0.0\" blue=\"0.0\" alpha=\"1\" colorSpace=\"custom\" customColorSpace=\"sRGB\"/>` */\n    color?: IBColor[];\n    nil?: IBItem<{\n      /** @example `textColor` `highlightedColor` */\n      key: string;\n    }>[];\n  }\n>;\n\nexport type IBTextAlignment = 'left' | 'center' | 'right' | 'justified' | 'natural';\n\nexport type IBContentMode = string | 'left' | 'scaleAspectFill';\n\nexport type IBConstraint = IBItem<{\n  firstItem: string;\n  firstAttribute: ConstraintAttribute;\n  secondItem: string;\n  secondAttribute: ConstraintAttribute;\n  constant?: number;\n  id: string;\n}>;\n\nexport type IBViewController = IBItem<\n  {\n    id: string;\n    placeholderIdentifier?: string;\n    userLabel: string;\n    sceneMemberID: string;\n  },\n  {\n    view: IBItem<\n      {\n        id: string;\n        key: string;\n        userInteractionEnabled: IBBoolean;\n        contentMode: string | 'scaleToFill';\n        insetsLayoutMarginsFromSafeArea: IBBoolean;\n        userLabel: string;\n      },\n      {\n        rect: IBRect[];\n        autoresizingMask: IBItem<{\n          key: string;\n          flexibleMaxX: IBBoolean;\n          flexibleMaxY: IBBoolean;\n        }>[];\n\n        subviews: IBItem<\n          object,\n          {\n            imageView: IBImageView[];\n            label: IBLabel[];\n          }\n        >[];\n        color: IBItem<{\n          key: string | 'backgroundColor';\n          name?: string;\n          systemColor?: string | 'systemBackgroundColor';\n          red?: string;\n          green?: string;\n          blue?: string;\n          alpha?: string;\n          colorSpace?: string;\n          customColorSpace?: string;\n        }>[];\n        constraints: IBItem<\n          object,\n          {\n            constraint: IBConstraint[];\n          }\n        >[];\n        viewLayoutGuide: IBItem<{\n          id: string;\n          key: string | 'safeArea';\n        }>[];\n      }\n    >[];\n  }\n>;\n\nexport type IBPoint = IBItem<{\n  key: string | 'canvasLocation';\n  x: number;\n  y: number;\n}>;\n\nexport type IBScene = IBItem<\n  { sceneID: string },\n  {\n    objects: {\n      viewController: IBViewController[];\n      placeholder: IBItem<{\n        id: string;\n        placeholderIdentifier?: string;\n        userLabel: string;\n        sceneMemberID: string;\n      }>[];\n    }[];\n    point: IBPoint[];\n  }\n>;\n\nexport type IBResourceImage = IBItem<{\n  name: string;\n  width: number;\n  height: number;\n}>;\n\nexport type IBResourceNamedColor = IBItem<{\n  name?: string;\n  systemColor?: string | 'systemBackgroundColor';\n  red?: string;\n  green?: string;\n  blue?: string;\n  alpha?: string;\n  colorSpace?: string;\n  customColorSpace?: string;\n}>;\n\nexport type IBDevice = IBItem<{\n  id: string;\n  orientation: string | 'portrait';\n  appearance: string | 'light';\n}>;\n\nexport type IBSplashScreenDocument = {\n  document: IBItem<\n    {\n      type: 'com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB' | string;\n      version: '3.0' | string;\n      toolsVersion: number;\n      targetRuntime: 'iOS.CocoaTouch' | string;\n      propertyAccessControl: 'none' | string;\n      useAutolayout: IBBoolean;\n      launchScreen: IBBoolean;\n      useTraitCollections: IBBoolean;\n      useSafeAreas: IBBoolean;\n      colorMatched: IBBoolean;\n      initialViewController: string;\n    },\n    {\n      device: IBDevice[];\n      dependencies: unknown[];\n      scenes: {\n        scene: IBScene[];\n      }[];\n      resources: {\n        image: IBResourceImage[];\n        namedColor?: IBItem<{ name: string }, { color: IBResourceNamedColor[] }>[];\n      }[];\n    }\n  >;\n};\n\nexport function createConstraint(\n  [firstItem, firstAttribute]: [string, ConstraintAttribute],\n  [secondItem, secondAttribute]: [string, ConstraintAttribute],\n  constant?: number\n): IBConstraint {\n  return {\n    $: {\n      firstItem,\n      firstAttribute,\n      secondItem,\n      secondAttribute,\n      constant,\n      // Prevent updating between runs\n      id: createConstraintId(firstItem, firstAttribute, secondItem, secondAttribute),\n    },\n  };\n}\n\nexport function createConstraintId(...attributes: string[]) {\n  return crypto.createHash('sha1').update(attributes.join('-')).digest('hex');\n}\n\nconst IMAGE_ID = 'EXPO-SplashScreen';\nconst CONTAINER_ID = 'EXPO-ContainerView';\n\nexport function removeImageFromSplashScreen(\n  xml: IBSplashScreenDocument,\n  { imageName }: { imageName: string }\n) {\n  const mainView = xml.document.scenes[0].scene[0].objects[0].viewController[0].view[0];\n\n  debug(`Remove all splash screen image elements`);\n\n  removeExisting(mainView.subviews[0].imageView, IMAGE_ID);\n\n  // Remove Constraints\n  getAbsoluteConstraints(IMAGE_ID, CONTAINER_ID).forEach((constraint) => {\n    // <constraint firstItem=\"EXPO-SplashScreen\" firstAttribute=\"top\" secondItem=\"EXPO-ContainerView\" secondAttribute=\"top\" id=\"2VS-Uz-0LU\"/>\n    const constrainsArray = mainView.constraints[0].constraint;\n    removeExisting(constrainsArray, constraint);\n  });\n\n  // Remove resource\n  xml.document.resources[0].image = xml.document.resources[0].image ?? [];\n  const imageSection = xml.document.resources[0].image;\n\n  const existingImageIndex = imageSection.findIndex((image) => image.$.name === imageName);\n  if (existingImageIndex && existingImageIndex > -1) {\n    imageSection?.splice(existingImageIndex, 1);\n  }\n  return xml;\n}\n\nfunction getAbsoluteConstraints(childId: string, parentId: string, legacy: boolean = false) {\n  if (legacy) {\n    return [\n      createConstraint([childId, 'top'], [parentId, 'top']),\n      createConstraint([childId, 'leading'], [parentId, 'leading']),\n      createConstraint([childId, 'trailing'], [parentId, 'trailing']),\n      createConstraint([childId, 'bottom'], [parentId, 'bottom']),\n    ];\n  }\n  return [\n    createConstraint([childId, 'centerX'], [parentId, 'centerX']),\n    createConstraint([childId, 'centerY'], [parentId, 'centerY']),\n  ];\n}\n\nexport function applyImageToSplashScreenXML(\n  xml: IBSplashScreenDocument,\n  {\n    imageName,\n    contentMode,\n    backgroundColor,\n    enableFullScreenImage,\n    imageWidth = 100,\n  }: {\n    imageName: string;\n    contentMode: ImageContentMode;\n    backgroundColor: string;\n    enableFullScreenImage: boolean;\n    imageWidth?: number;\n  }\n): IBSplashScreenDocument {\n  const mainView = xml.document.scenes[0].scene[0].objects[0].viewController[0].view[0];\n  const width = enableFullScreenImage ? 414 : imageWidth;\n  const height = enableFullScreenImage ? 736 : imageWidth;\n  const x = enableFullScreenImage ? 0 : (mainView.rect[0].$.width - width) / 2;\n  const y = enableFullScreenImage ? 0 : (mainView.rect[0].$.height - height) / 2;\n\n  const imageView: IBImageView = {\n    $: {\n      id: IMAGE_ID,\n      userLabel: imageName,\n      image: imageName,\n      contentMode,\n      clipsSubviews: true,\n      userInteractionEnabled: false,\n      translatesAutoresizingMaskIntoConstraints: false,\n    },\n    rect: [\n      {\n        $: {\n          key: 'frame',\n          x,\n          y,\n          width,\n          height,\n        },\n      },\n    ],\n  };\n\n  // Add ImageView\n  ensureUniquePush(mainView.subviews[0].imageView, imageView);\n\n  mainView.constraints[0].constraint = [];\n\n  // Add Constraints\n  getAbsoluteConstraints(IMAGE_ID, CONTAINER_ID, enableFullScreenImage).forEach(\n    (constraint: IBConstraint) => {\n      const constrainsArray = mainView.constraints[0].constraint;\n      ensureUniquePush(constrainsArray, constraint);\n    }\n  );\n\n  // Add resource\n  xml.document.resources[0].image = xml.document.resources[0].image ?? [];\n  const imageSection = xml.document.resources[0].image;\n\n  const existingImageIndex = imageSection.findIndex((image) => image.$.name === imageName);\n  if (existingImageIndex > -1) {\n    debug(`Removing existing IB image asset at index ${existingImageIndex}`);\n    imageSection.splice(existingImageIndex, 1);\n  }\n  imageSection.push({\n    $: {\n      name: imageName,\n      width,\n      height,\n    },\n  });\n\n  // Clear existing color\n  mainView.color = [];\n  // Add background color\n  const colorSection = mainView.color;\n\n  colorSection.push({\n    $: {\n      key: 'backgroundColor',\n      name: 'SplashScreenBackground',\n    },\n  });\n\n  // Clear existing named colors\n  const namedColorSection = [];\n  // Add background named color reference\n  const color = parseColor(backgroundColor);\n\n  namedColorSection.push({\n    $: {\n      name: 'SplashScreenBackground',\n    },\n    color: [\n      {\n        $: {\n          alpha: '1.000',\n          blue: color.rgb.blue,\n          green: color.rgb.green,\n          red: color.rgb.red,\n          customColorSpace: 'sRGB',\n          colorSpace: 'custom',\n        },\n      },\n    ],\n  });\n\n  xml.document.resources[0].namedColor = namedColorSection;\n\n  return xml;\n}\n\n/**\n * IB does not allow two items to have the same ID.\n * This method will add an item by first removing any existing item with the same `$.id`.\n */\nexport function ensureUniquePush<TItem extends { $: { id: string } }>(array: TItem[], item: TItem) {\n  if (!array) return array;\n  removeExisting(array, item);\n  array.push(item);\n  return array;\n}\n\nexport function removeExisting<TItem extends { $: { id: string } }>(\n  array: TItem[],\n  item: TItem | string\n) {\n  const id = typeof item === 'string' ? item : item.$?.id;\n  const existingItem = array?.findIndex((existingItem) => existingItem.$.id === id);\n  if (existingItem > -1) {\n    debug(`Removing existing IB item with id ${id}, from: %O`, array);\n    array.splice(existingItem, 1);\n  }\n  return array;\n}\n\n// Attempt to copy Xcode formatting.\nexport function toString(xml: any): string {\n  const builder = new Builder({\n    // @ts-expect-error: untyped\n    preserveChildrenOrder: true,\n    xmldec: {\n      version: '1.0',\n      encoding: 'UTF-8',\n    },\n    renderOpts: {\n      pretty: true,\n      indent: '    ',\n    },\n  });\n  return builder.buildObject(xml);\n}\n\n/** Parse string contents into an object. */\nexport function toObjectAsync(contents: string) {\n  return new Parser().parseStringPromise(contents);\n}\n\n// Function taken from react-native-bootsplash\nexport const parseColor = (value: string): Color => {\n  const color = value.toUpperCase().replace(/[^0-9A-F]/g, '');\n\n  if (color.length !== 3 && color.length !== 6) {\n    console.error(`\"${value}\" value is not a valid hexadecimal color.`);\n    process.exit(1);\n  }\n\n  const hex =\n    color.length === 3\n      ? '#' + color[0] + color[0] + color[1] + color[1] + color[2] + color[2]\n      : '#' + color;\n\n  const rgb: Color['rgb'] = {\n    red: (parseInt('' + hex[1] + hex[2], 16) / 255).toPrecision(15),\n    green: (parseInt('' + hex[3] + hex[4], 16) / 255).toPrecision(15),\n    blue: (parseInt('' + hex[5] + hex[6], 16) / 255).toPrecision(15),\n  };\n\n  return { hex, rgb };\n};\n\ntype Color = {\n  hex: string;\n  rgb: {\n    red: string;\n    green: string;\n    blue: string;\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAyC,SAAAC,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEzC,MAAMG,KAAK,GAAGL,OAAO,CAAC,OAAO,CAAC,CAC5B,8DACF,CAAuB;;AA4BvB;;AAuPO,SAASM,gBAAgBA,CAC9B,CAACC,SAAS,EAAEC,cAAc,CAAgC,EAC1D,CAACC,UAAU,EAAEC,eAAe,CAAgC,EAC5DC,QAAiB,EACH;EACd,OAAO;IACLC,CAAC,EAAE;MACDL,SAAS;MACTC,cAAc;MACdC,UAAU;MACVC,eAAe;MACfC,QAAQ;MACR;MACAE,EAAE,EAAEC,kBAAkB,CAACP,SAAS,EAAEC,cAAc,EAAEC,UAAU,EAAEC,eAAe;IAC/E;EACF,CAAC;AACH;AAEO,SAASI,kBAAkBA,CAAC,GAAGC,UAAoB,EAAE;EAC1D,OAAOC,iBAAM,CAACC,UAAU,CAAC,MAAM,CAAC,CAACC,MAAM,CAACH,UAAU,CAACI,IAAI,CAAC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,KAAK,CAAC;AAC7E;AAEA,MAAMC,QAAQ,GAAG,mBAAmB;AACpC,MAAMC,YAAY,GAAG,oBAAoB;AAElC,SAASC,2BAA2BA,CACzCC,GAA2B,EAC3B;EAAEC;AAAiC,CAAC,EACpC;EACA,MAAMC,QAAQ,GAAGF,GAAG,CAACG,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;EAErF3B,KAAK,CAAC,yCAAyC,CAAC;EAEhD4B,cAAc,CAACP,QAAQ,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACC,SAAS,EAAEd,QAAQ,CAAC;;EAExD;EACAe,sBAAsB,CAACf,QAAQ,EAAEC,YAAY,CAAC,CAACe,OAAO,CAAEC,UAAU,IAAK;IACrE;IACA,MAAMC,eAAe,GAAGb,QAAQ,CAACc,WAAW,CAAC,CAAC,CAAC,CAACF,UAAU;IAC1DL,cAAc,CAACM,eAAe,EAAED,UAAU,CAAC;EAC7C,CAAC,CAAC;;EAEF;EACAd,GAAG,CAACG,QAAQ,CAACc,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGlB,GAAG,CAACG,QAAQ,CAACc,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,IAAI,EAAE;EACvE,MAAMC,YAAY,GAAGnB,GAAG,CAACG,QAAQ,CAACc,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK;EAEpD,MAAME,kBAAkB,GAAGD,YAAY,CAACE,SAAS,CAAEH,KAAK,IAAKA,KAAK,CAAC9B,CAAC,CAACkC,IAAI,KAAKrB,SAAS,CAAC;EACxF,IAAImB,kBAAkB,IAAIA,kBAAkB,GAAG,CAAC,CAAC,EAAE;IACjDD,YAAY,EAAEI,MAAM,CAACH,kBAAkB,EAAE,CAAC,CAAC;EAC7C;EACA,OAAOpB,GAAG;AACZ;AAEA,SAASY,sBAAsBA,CAACY,OAAe,EAAEC,QAAgB,EAAEC,MAAe,GAAG,KAAK,EAAE;EAC1F,IAAIA,MAAM,EAAE;IACV,OAAO,CACL5C,gBAAgB,CAAC,CAAC0C,OAAO,EAAE,KAAK,CAAC,EAAE,CAACC,QAAQ,EAAE,KAAK,CAAC,CAAC,EACrD3C,gBAAgB,CAAC,CAAC0C,OAAO,EAAE,SAAS,CAAC,EAAE,CAACC,QAAQ,EAAE,SAAS,CAAC,CAAC,EAC7D3C,gBAAgB,CAAC,CAAC0C,OAAO,EAAE,UAAU,CAAC,EAAE,CAACC,QAAQ,EAAE,UAAU,CAAC,CAAC,EAC/D3C,gBAAgB,CAAC,CAAC0C,OAAO,EAAE,QAAQ,CAAC,EAAE,CAACC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAC5D;EACH;EACA,OAAO,CACL3C,gBAAgB,CAAC,CAAC0C,OAAO,EAAE,SAAS,CAAC,EAAE,CAACC,QAAQ,EAAE,SAAS,CAAC,CAAC,EAC7D3C,gBAAgB,CAAC,CAAC0C,OAAO,EAAE,SAAS,CAAC,EAAE,CAACC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAC9D;AACH;AAEO,SAASE,2BAA2BA,CACzC3B,GAA2B,EAC3B;EACEC,SAAS;EACT2B,WAAW;EACXC,eAAe;EACfC,qBAAqB;EACrBC,UAAU,GAAG;AAOf,CAAC,EACuB;EACxB,MAAM7B,QAAQ,GAAGF,GAAG,CAACG,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;EACrF,MAAMwB,KAAK,GAAGF,qBAAqB,GAAG,GAAG,GAAGC,UAAU;EACtD,MAAME,MAAM,GAAGH,qBAAqB,GAAG,GAAG,GAAGC,UAAU;EACvD,MAAMG,CAAC,GAAGJ,qBAAqB,GAAG,CAAC,GAAG,CAAC5B,QAAQ,CAACiC,IAAI,CAAC,CAAC,CAAC,CAAC/C,CAAC,CAAC4C,KAAK,GAAGA,KAAK,IAAI,CAAC;EAC5E,MAAMI,CAAC,GAAGN,qBAAqB,GAAG,CAAC,GAAG,CAAC5B,QAAQ,CAACiC,IAAI,CAAC,CAAC,CAAC,CAAC/C,CAAC,CAAC6C,MAAM,GAAGA,MAAM,IAAI,CAAC;EAE9E,MAAMtB,SAAsB,GAAG;IAC7BvB,CAAC,EAAE;MACDC,EAAE,EAAEQ,QAAQ;MACZwC,SAAS,EAAEpC,SAAS;MACpBiB,KAAK,EAAEjB,SAAS;MAChB2B,WAAW;MACXU,aAAa,EAAE,IAAI;MACnBC,sBAAsB,EAAE,KAAK;MAC7BC,yCAAyC,EAAE;IAC7C,CAAC;IACDL,IAAI,EAAE,CACJ;MACE/C,CAAC,EAAE;QACDqD,GAAG,EAAE,OAAO;QACZP,CAAC;QACDE,CAAC;QACDJ,KAAK;QACLC;MACF;IACF,CAAC;EAEL,CAAC;;EAED;EACAS,gBAAgB,CAACxC,QAAQ,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACC,SAAS,EAAEA,SAAS,CAAC;EAE3DT,QAAQ,CAACc,WAAW,CAAC,CAAC,CAAC,CAACF,UAAU,GAAG,EAAE;;EAEvC;EACAF,sBAAsB,CAACf,QAAQ,EAAEC,YAAY,EAAEgC,qBAAqB,CAAC,CAACjB,OAAO,CAC1EC,UAAwB,IAAK;IAC5B,MAAMC,eAAe,GAAGb,QAAQ,CAACc,WAAW,CAAC,CAAC,CAAC,CAACF,UAAU;IAC1D4B,gBAAgB,CAAC3B,eAAe,EAAED,UAAU,CAAC;EAC/C,CACF,CAAC;;EAED;EACAd,GAAG,CAACG,QAAQ,CAACc,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGlB,GAAG,CAACG,QAAQ,CAACc,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,IAAI,EAAE;EACvE,MAAMC,YAAY,GAAGnB,GAAG,CAACG,QAAQ,CAACc,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK;EAEpD,MAAME,kBAAkB,GAAGD,YAAY,CAACE,SAAS,CAAEH,KAAK,IAAKA,KAAK,CAAC9B,CAAC,CAACkC,IAAI,KAAKrB,SAAS,CAAC;EACxF,IAAImB,kBAAkB,GAAG,CAAC,CAAC,EAAE;IAC3BvC,KAAK,CAAC,6CAA6CuC,kBAAkB,EAAE,CAAC;IACxED,YAAY,CAACI,MAAM,CAACH,kBAAkB,EAAE,CAAC,CAAC;EAC5C;EACAD,YAAY,CAACwB,IAAI,CAAC;IAChBvD,CAAC,EAAE;MACDkC,IAAI,EAAErB,SAAS;MACf+B,KAAK;MACLC;IACF;EACF,CAAC,CAAC;;EAEF;EACA/B,QAAQ,CAAC0C,KAAK,GAAG,EAAE;EACnB;EACA,MAAMC,YAAY,GAAG3C,QAAQ,CAAC0C,KAAK;EAEnCC,YAAY,CAACF,IAAI,CAAC;IAChBvD,CAAC,EAAE;MACDqD,GAAG,EAAE,iBAAiB;MACtBnB,IAAI,EAAE;IACR;EACF,CAAC,CAAC;;EAEF;EACA,MAAMwB,iBAAiB,GAAG,EAAE;EAC5B;EACA,MAAMF,KAAK,GAAGG,UAAU,CAAClB,eAAe,CAAC;EAEzCiB,iBAAiB,CAACH,IAAI,CAAC;IACrBvD,CAAC,EAAE;MACDkC,IAAI,EAAE;IACR,CAAC;IACDsB,KAAK,EAAE,CACL;MACExD,CAAC,EAAE;QACD4D,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEL,KAAK,CAACM,GAAG,CAACD,IAAI;QACpBE,KAAK,EAAEP,KAAK,CAACM,GAAG,CAACC,KAAK;QACtBC,GAAG,EAAER,KAAK,CAACM,GAAG,CAACE,GAAG;QAClBC,gBAAgB,EAAE,MAAM;QACxBC,UAAU,EAAE;MACd;IACF,CAAC;EAEL,CAAC,CAAC;EAEFtD,GAAG,CAACG,QAAQ,CAACc,SAAS,CAAC,CAAC,CAAC,CAACsC,UAAU,GAAGT,iBAAiB;EAExD,OAAO9C,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACO,SAAS0C,gBAAgBA,CAAsCc,KAAc,EAAEC,IAAW,EAAE;EACjG,IAAI,CAACD,KAAK,EAAE,OAAOA,KAAK;EACxB/C,cAAc,CAAC+C,KAAK,EAAEC,IAAI,CAAC;EAC3BD,KAAK,CAACb,IAAI,CAACc,IAAI,CAAC;EAChB,OAAOD,KAAK;AACd;AAEO,SAAS/C,cAAcA,CAC5B+C,KAAc,EACdC,IAAoB,EACpB;EACA,MAAMpE,EAAE,GAAG,OAAOoE,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACrE,CAAC,EAAEC,EAAE;EACvD,MAAMqE,YAAY,GAAGF,KAAK,EAAEnC,SAAS,CAAEqC,YAAY,IAAKA,YAAY,CAACtE,CAAC,CAACC,EAAE,KAAKA,EAAE,CAAC;EACjF,IAAIqE,YAAY,GAAG,CAAC,CAAC,EAAE;IACrB7E,KAAK,CAAC,qCAAqCQ,EAAE,YAAY,EAAEmE,KAAK,CAAC;IACjEA,KAAK,CAACjC,MAAM,CAACmC,YAAY,EAAE,CAAC,CAAC;EAC/B;EACA,OAAOF,KAAK;AACd;;AAEA;AACO,SAASG,QAAQA,CAAC3D,GAAQ,EAAU;EACzC,MAAM4D,OAAO,GAAG,KAAIC,iBAAO,EAAC;IAC1B;IACAC,qBAAqB,EAAE,IAAI;IAC3BC,MAAM,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE;IACZ,CAAC;IACDC,UAAU,EAAE;MACVC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACF,OAAOR,OAAO,CAACS,WAAW,CAACrE,GAAG,CAAC;AACjC;;AAEA;AACO,SAASsE,aAAaA,CAACC,QAAgB,EAAE;EAC9C,OAAO,KAAIC,gBAAM,EAAC,CAAC,CAACC,kBAAkB,CAACF,QAAQ,CAAC;AAClD;;AAEA;AACO,MAAMxB,UAAU,GAAI2B,KAAa,IAAY;EAClD,MAAM9B,KAAK,GAAG8B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;EAE3D,IAAIhC,KAAK,CAACiC,MAAM,KAAK,CAAC,IAAIjC,KAAK,CAACiC,MAAM,KAAK,CAAC,EAAE;IAC5CC,OAAO,CAACC,KAAK,CAAC,IAAIL,KAAK,2CAA2C,CAAC;IACnEM,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;EACjB;EAEA,MAAMC,GAAG,GACPtC,KAAK,CAACiC,MAAM,KAAK,CAAC,GACd,GAAG,GAAGjC,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GACrE,GAAG,GAAGA,KAAK;EAEjB,MAAMM,GAAiB,GAAG;IACxBE,GAAG,EAAE,CAAC+B,QAAQ,CAAC,EAAE,GAAGD,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAEE,WAAW,CAAC,EAAE,CAAC;IAC/DjC,KAAK,EAAE,CAACgC,QAAQ,CAAC,EAAE,GAAGD,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAEE,WAAW,CAAC,EAAE,CAAC;IACjEnC,IAAI,EAAE,CAACkC,QAAQ,CAAC,EAAE,GAAGD,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAEE,WAAW,CAAC,EAAE;EACjE,CAAC;EAED,OAAO;IAAEF,GAAG;IAAEhC;EAAI,CAAC;AACrB,CAAC;AAACmC,OAAA,CAAAtC,UAAA,GAAAA,UAAA", "ignoreList": []}