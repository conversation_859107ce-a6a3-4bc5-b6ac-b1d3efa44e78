{"version": 3, "file": "withRestoreDefaultTheme.js", "names": ["_configPlugins", "data", "require", "withRestoreDefaultTheme", "config", "withAndroidStyles", "restoreDefaultTheme", "exports", "DEFAULT_THEME", "style", "modResults", "resources", "mainThemeIndex", "findIndex", "$", "name", "parent", "includes", "item", "filter"], "sources": ["../../../../src/plugins/unversioned/react-native-edge-to-edge/withRestoreDefaultTheme.ts"], "sourcesContent": ["import { ConfigPlugin, withAndroidStyles } from '@expo/config-plugins';\n\nimport { ResourceXMLConfig } from './withEdgeToEdge';\n\nexport const withRestoreDefaultTheme: ConfigPlugin = (config) => {\n  // Default theme for SDK 53 and onwards projects\n  return withAndroidStyles(config, (config) => {\n    return restoreDefaultTheme(config);\n  });\n};\n\nexport function restoreDefaultTheme(config: ResourceXMLConfig): ResourceXMLConfig {\n  const DEFAULT_THEME = 'Theme.AppCompat.DayNight.NoActionBar';\n\n  const { style = [] } = config.modResults.resources;\n  const mainThemeIndex = style.findIndex(({ $ }) => $.name === 'AppTheme');\n  if (mainThemeIndex === -1) {\n    return config;\n  }\n\n  if (style[mainThemeIndex].$?.parent.includes('EdgeToEdge')) {\n    config.modResults.resources.style = [\n      {\n        $: {\n          name: 'AppTheme',\n          parent: DEFAULT_THEME,\n        },\n        item: style[mainThemeIndex].item,\n      },\n      ...style.filter(({ $ }) => $.name !== 'AppTheme'),\n    ];\n  }\n  return config;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIO,MAAME,uBAAqC,GAAIC,MAAM,IAAK;EAC/D;EACA,OAAO,IAAAC,kCAAiB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC3C,OAAOE,mBAAmB,CAACF,MAAM,CAAC;EACpC,CAAC,CAAC;AACJ,CAAC;AAACG,OAAA,CAAAJ,uBAAA,GAAAA,uBAAA;AAEK,SAASG,mBAAmBA,CAACF,MAAyB,EAAqB;EAChF,MAAMI,aAAa,GAAG,sCAAsC;EAE5D,MAAM;IAAEC,KAAK,GAAG;EAAG,CAAC,GAAGL,MAAM,CAACM,UAAU,CAACC,SAAS;EAClD,MAAMC,cAAc,GAAGH,KAAK,CAACI,SAAS,CAAC,CAAC;IAAEC;EAAE,CAAC,KAAKA,CAAC,CAACC,IAAI,KAAK,UAAU,CAAC;EACxE,IAAIH,cAAc,KAAK,CAAC,CAAC,EAAE;IACzB,OAAOR,MAAM;EACf;EAEA,IAAIK,KAAK,CAACG,cAAc,CAAC,CAACE,CAAC,EAAEE,MAAM,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;IAC1Db,MAAM,CAACM,UAAU,CAACC,SAAS,CAACF,KAAK,GAAG,CAClC;MACEK,CAAC,EAAE;QACDC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAER;MACV,CAAC;MACDU,IAAI,EAAET,KAAK,CAACG,cAAc,CAAC,CAACM;IAC9B,CAAC,EACD,GAAGT,KAAK,CAACU,MAAM,CAAC,CAAC;MAAEL;IAAE,CAAC,KAAKA,CAAC,CAACC,IAAI,KAAK,UAAU,CAAC,CAClD;EACH;EACA,OAAOX,MAAM;AACf", "ignoreList": []}