{"name": "@expo/rudder-sdk-node", "version": "1.1.1", "description": "Compact fork of rudder-node-sdk", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/expo/rudder-sdk-node.git"}, "author": "Expo", "engines": {"node": ">=12"}, "size-limit": [{"limit": "25 KB", "path": "index.js"}], "scripts": {"dependencies": "yarn", "size": "size-limit", "watch": "tsc --watch", "build": "tsc", "prepare": "tsc", "test": "nyc --reporter=lcov --reporter=html --reporter=text ava --serial --verbose  > coverage.lcov", "lint": "eslint .", "changelog": "auto-changelog -p -t keepachangelog -u true -l false --sort-commits date-desc "}, "main": "index.js", "types": "index.d.ts", "files": ["cli.js", "index.d.ts", "index.js", "index.js.map"], "keywords": ["analytics"], "dependencies": {"@expo/bunyan": "^4.0.0", "@segment/loosely-validate-event": "^2.0.0", "fetch-retry": "^4.1.1", "md5": "^2.2.1", "node-fetch": "^2.6.1", "remove-trailing-slash": "^0.1.0", "uuid": "^8.3.2"}, "devDependencies": {"@babel/core": "^7.15.0", "@tsconfig/node12": "^1.0.9", "@types/jest": "^27.0.1", "@types/md5": "^2.3.1", "@types/node-fetch": "^2.5.12", "@types/uuid": "^8.3.1", "auto-changelog": "^1.16.2", "ava": "^0.25.0", "basic-auth": "^2.0.1", "body-parser": "^1.17.1", "commander": "^2.9.0", "delay": "^4.2.0", "eslint": "^7.32.0", "eslint-config-universe": "^7.0.1", "express": "^4.15.2", "fetch-mock": "^9.11.0", "jest": "^27.1.0", "nyc": "^14.1.1", "prettier": "^2.3.2", "sinon": "^7.3.2", "size-limit": "^1.3.5", "ts-jest": "~27.0.5", "typescript": "^4.3.5"}}