{"version": "15.12.0", "name": "react-native-svg", "description": "SVG library for react-native", "homepage": "https://github.com/react-native-community/react-native-svg", "repository": {"type": "git", "url": "https://github.com/react-native-community/react-native-svg"}, "license": "MIT", "main": "lib/commonjs/index.js", "module": "lib/module/index.js", "types": "lib/typescript/index.d.ts", "react-native": "src/index.ts", "files": ["android", "apple", "common", "lib", "src", "css", "filter-image", "RNSVG.podspec", "!android/build", "windows", "react-native.config.js", "scripts/rnsvg_utils.rb"], "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}, "keywords": ["react-component", "react-native", "ios", "android", "windows", "SVG", "ART", "VML", "gradient"], "scripts": {"bob": "bob build", "format": "yarn format-js && yarn format-ios && yarn format-java", "format-ios": "find apple/ common/ -iname *.h -o -iname *.m -o -iname *.cpp -o -iname *.mm | xargs clang-format -i", "format-java": "node ./scripts/format-java.js", "format-js": "prettier --write README.md CONTRIBUTING.md CODE_OF_CONDUCT.md USAGE.md ./src/**/*.{ts,tsx} ./apps/**/*.{ts,tsx}", "jest": "jest", "lint": "eslint --ext .ts,.tsx src", "peg": "pegjs -o src/lib/extract/transform.js ./src/lib/extract/transform.peg && peggy -o src/filter-image/extract/extractFiltersString.js src/filter-image/extract/extractFiltersString.pegjs && peggy -o src/lib/extract/transformToRn.js src/lib/extract/transformToRn.pegjs", "prepare": "npm run bob && husky install", "release": "npm login && release-it", "test": "npm run lint && npm run tsc", "tsc": "tsc --noEmit", "e2e": "jest e2e", "generateE2eReferences": "ts-node e2e/generateReferences.ts", "check-archs-consistency": "node ./scripts/codegen-check-consistency.js", "sync-archs": "node ./scripts/codegen-sync-archs.js", "metal-to-ci": "node ./scripts/metal.js"}, "peerDependencies": {"react": "*", "react-native": "*"}, "dependencies": {"css-select": "^5.1.0", "css-tree": "^1.1.3", "warn-once": "0.1.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@react-native/babel-preset": "^0.77.0", "@react-native/eslint-config": "^0.77.0", "@types/css-tree": "^1.0.3", "@types/jest": "^27.5.2", "@types/node": "*", "@types/pixelmatch": "^5.2.0", "@types/pngjs": "^6.0.5", "@types/react": "^18.3.12", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^5.11.0", "@typescript-eslint/parser": "^5.11.0", "babel-plugin-module-resolver": "^5.0.0", "clang-format": "^1.8.0", "eslint": "^8.44.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^17.0.0", "eslint-import-resolver-babel-module": "^5.3.2", "eslint-plugin-import": "^2.25.4", "eslint-plugin-n": "^16.0.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-standard": "^5.0.0", "husky": "^8.0.1", "jest": "^28.1.0", "jest-html-reporters": "^3.1.7", "lint-staged": "^13.0.3", "peggy": "4.0.3", "pegjs": "^0.10.0", "pixelmatch": "5.3.0", "pngjs": "^7.0.0", "prettier": "3.0.1", "puppeteer": "^22.12.1", "react": "^18.3.1", "react-native": "^0.77.0", "react-native-builder-bob": "^0.20.4", "react-native-windows": "^0.76.3", "react-test-renderer": "^18.2.0", "release-it": "^14.12.5", "ts-node": "^10.9.2", "typescript": "^5.1.6", "ws": "^8.18.0"}, "lint-staged": {"{src,apps/common}/**/*.{js,ts,tsx}": "yarn format-js", "src/**/*.{js,ts,tsx}": "yarn lint", "apple/**/*.{h,m,mm,cpp}": "yarn format-ios", "android/src/**/*.java": "yarn format-java", "src/fabric/*.ts": "yarn sync-archs"}, "nativePackage": true, "codegenConfig": {"name": "rnsvg", "type": "all", "jsSrcsDir": "./src/fabric", "android": {"javaPackageName": "com.horcrux.svg"}, "ios": {"componentProvider": {"RNSVGCircle": "RNSVGCircle", "RNSVGClipPath": "RNSVGClipPath", "RNSVGDefs": "RNSVGDefs", "RNSVGEllipse": "RNSVGEllipse", "RNSVGFeBlend": "RNSVGFeBlend", "RNSVGFeColorMatrix": "RNSVGFeColorMatrix", "RNSVGFeComposite": "RNSVGFeComposite", "RNSVGFeFlood": "RNSVGFeFlood", "RNSVGFeGaussianBlur": "RNSVGFeGaussianBlur", "RNSVGFeMerge": "RNSVGFeMerge", "RNSVGFeOffset": "RNSVGFeOffset", "RNSVGFilter": "RNSVGFilter", "RNSVGForeignObject": "RNSVGForeignObject", "RNSVGGroup": "RNSVGGroup", "RNSVGImage": "RNSVGImage", "RNSVGLine": "RNSVGLine", "RNSVGLinearGradient": "RNSVGLinearGradient", "RNSVGMarker": "RNSVGMarker", "RNSVGMask": "RNSVGMask", "RNSVGPath": "RNSVGPath", "RNSVGPattern": "RNSVGPattern", "RNSVGRadialGradient": "RNSVGRadialGradient", "RNSVGRect": "RNSVGRect", "RNSVGSvgView": "RNSVGSvgView", "RNSVGSymbol": "RNSVGSymbol", "RNSVGTSpan": "RNSVGTSpan", "RNSVGText": "RNSVGText", "RNSVGTextPath": "RNSVGTextPath", "RNSVGUse": "RNSVGUse"}}}, "packageManager": "yarn@1.22.22"}