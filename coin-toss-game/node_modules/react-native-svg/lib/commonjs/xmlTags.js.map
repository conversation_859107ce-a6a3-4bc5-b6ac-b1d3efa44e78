{"version": 3, "names": ["_elements", "require", "tags", "exports", "circle", "Circle", "clipPath", "<PERSON><PERSON><PERSON><PERSON>", "defs", "Defs", "ellipse", "Ellipse", "filter", "Filter", "feBlend", "FeBlend", "feColorMatrix", "FeColorMatrix", "feComponentTransfer", "FeComponentTransfer", "feComposite", "FeComposite", "feConvolveMatrix", "FeConvolveMatrix", "feDiffuseLighting", "FeDiffuseLighting", "feDisplacementMap", "FeDisplacementMap", "feDistantLight", "FeDistantLight", "feDropShadow", "FeDropShadow", "feFlood", "FeFlood", "feG<PERSON><PERSON><PERSON>lur", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "feImage", "FeImage", "feMerge", "FeMerge", "feMergeNode", "FeMergeNode", "feMorphology", "FeMorphology", "feOffset", "FeOffset", "fePointLight", "FePointLight", "feSpecularLighting", "FeSpecularLighting", "feSpotLight", "FeSpotLight", "feTile", "FeTile", "feTurbulence", "FeTurbulence", "foreignObject", "ForeignObject", "g", "G", "image", "Image", "line", "Line", "linearGradient", "LinearGradient", "marker", "<PERSON><PERSON>", "mask", "Mask", "path", "Path", "pattern", "Pattern", "polygon", "Polygon", "polyline", "Polyline", "radialGradient", "RadialGrad<PERSON>", "rect", "Rect", "stop", "Stop", "svg", "Svg", "symbol", "Symbol", "text", "Text", "textPath", "TextPath", "tspan", "TSpan", "use", "Use"], "sourceRoot": "../../src", "sources": ["xmlTags.ts"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAiDO,MAAMC,IAAI,GAAAC,OAAA,CAAAD,IAAA,GAAG;EAClBE,MAAM,EAAEC,gBAAM;EACdC,QAAQ,EAAEC,kBAAQ;EAClBC,IAAI,EAAEC,cAAI;EACVC,OAAO,EAAEC,iBAAO;EAChBC,MAAM,EAAEC,gBAAM;EACdC,OAAO,EAAEC,iBAAO;EAChBC,aAAa,EAAEC,uBAAa;EAC5BC,mBAAmB,EAAEC,6BAAmB;EACxCC,WAAW,EAAEC,qBAAW;EACxBC,gBAAgB,EAAEC,0BAAgB;EAClCC,iBAAiB,EAAEC,2BAAiB;EACpCC,iBAAiB,EAAEC,2BAAiB;EACpCC,cAAc,EAAEC,wBAAc;EAC9BC,YAAY,EAAEC,sBAAY;EAC1BC,OAAO,EAAEC,iBAAO;EAChBC,cAAc,EAAEC,wBAAc;EAC9BC,OAAO,EAAEC,iBAAO;EAChBC,OAAO,EAAEC,iBAAO;EAChBC,WAAW,EAAEC,qBAAW;EACxBC,YAAY,EAAEC,sBAAY;EAC1BC,QAAQ,EAAEC,kBAAQ;EAClBC,YAAY,EAAEC,sBAAY;EAC1BC,kBAAkB,EAAEC,4BAAkB;EACtCC,WAAW,EAAEC,qBAAW;EACxBC,MAAM,EAAEC,gBAAM;EACdC,YAAY,EAAEC,sBAAY;EAC1BC,aAAa,EAAEC,uBAAa;EAC5BC,CAAC,EAAEC,WAAC;EACJC,KAAK,EAAEC,eAAK;EACZC,IAAI,EAAEC,cAAI;EACVC,cAAc,EAAEC,wBAAc;EAC9BC,MAAM,EAAEC,gBAAM;EACdC,IAAI,EAAEC,cAAI;EACVC,IAAI,EAAEC,cAAI;EACVC,OAAO,EAAEC,iBAAO;EAChBC,OAAO,EAAEC,iBAAO;EAChBC,QAAQ,EAAEC,kBAAQ;EAClBC,cAAc,EAAEC,wBAAc;EAC9BC,IAAI,EAAEC,cAAI;EACVC,IAAI,EAAEC,cAAI;EACVC,GAAG,EAAEC,aAAG;EACRC,MAAM,EAAEC,gBAAM;EACdC,IAAI,EAAEC,cAAI;EACVC,QAAQ,EAAEC,kBAAQ;EAClBC,KAAK,EAAEC,eAAK;EACZC,GAAG,EAAEC;AACP,CAAU", "ignoreList": []}