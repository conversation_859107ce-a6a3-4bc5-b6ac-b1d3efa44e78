{"version": 3, "names": ["_reactNative", "require", "resolve", "styleProp", "cleanedProps", "StyleSheet", "Symbol", "iterator", "Object", "assign"], "sourceRoot": "../../../src", "sources": ["lib/resolve.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA;AACO,SAASC,OAAOA,CACrBC,SAAkC,EAClCC,YAAe,EACf;EACA,IAAID,SAAS,EAAE;IACb,OAAOE,uBAAU,GACb,CAACF,SAAS,EAAEC,YAAY,CAAC;IACzB;IACFD,SAAS,CAACG,MAAM,CAACC,QAAQ,CAAC,GACxBC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGN,SAAS,EAAEC,YAAY,CAAC,GAC7CI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,SAAS,EAAEC,YAAY,CAAC;EAChD,CAAC,MAAM;IACL,OAAOA,YAAY;EACrB;AACF", "ignoreList": []}