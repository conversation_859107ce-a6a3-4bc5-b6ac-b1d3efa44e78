import type { Float, Int32, WithDefault } from 'react-native/Libraries/Types/CodegenTypes';
import type { ViewProps } from './utils';
import type { UnsafeMixed } from './codegenUtils';
import { NumberProp } from '../lib/extract/types';
interface SvgNodeCommonProps {
    name?: string;
    opacity?: WithDefault<Float, 1.0>;
    matrix?: ReadonlyArray<Float>;
    mask?: string;
    markerStart?: string;
    markerMid?: string;
    markerEnd?: string;
    clipPath?: string;
    clipRule?: WithDefault<Int32, 0>;
    responsible?: boolean;
    display?: string;
    pointerEvents?: string;
}
interface NativeProps extends ViewProps, SvgNodeCommonProps {
    fx?: UnsafeMixed<NumberProp>;
    fy?: UnsafeMixed<NumberProp>;
    cx?: UnsafeMixed<NumberProp>;
    cy?: UnsafeMixed<NumberProp>;
    rx?: UnsafeMixed<NumberProp>;
    ry?: UnsafeMixed<NumberProp>;
    gradient?: ReadonlyArray<Float>;
    gradientUnits?: Int32;
    gradientTransform?: ReadonlyArray<Float> | null;
}
declare const _default: import("react-native/Libraries/Utilities/codegenNativeComponent").NativeComponentType<NativeProps>;
export default _default;
//# sourceMappingURL=RadialGradientNativeComponent.d.ts.map