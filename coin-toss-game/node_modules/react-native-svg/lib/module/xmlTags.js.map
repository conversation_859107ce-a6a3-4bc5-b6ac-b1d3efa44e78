{"version": 3, "names": ["Circle", "<PERSON><PERSON><PERSON><PERSON>", "Defs", "Ellipse", "FeBlend", "FeColorMatrix", "FeComponentTransfer", "FeComposite", "FeConvolveMatrix", "FeDiffuseLighting", "FeDisplacementMap", "FeDistantLight", "FeDropShadow", "FeFlood", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FeImage", "FeMerge", "FeMergeNode", "FeMorphology", "FeOffset", "FePointLight", "FeSpecularLighting", "FeSpotLight", "FeTile", "FeTurbulence", "Filter", "ForeignObject", "G", "Image", "Line", "LinearGradient", "<PERSON><PERSON>", "Mask", "Path", "Pattern", "Polygon", "Polyline", "RadialGrad<PERSON>", "Rect", "Stop", "Svg", "Symbol", "Text", "TextPath", "TSpan", "Use", "tags", "circle", "clipPath", "defs", "ellipse", "filter", "feBlend", "feColorMatrix", "feComponentTransfer", "feComposite", "feConvolveMatrix", "feDiffuseLighting", "feDisplacementMap", "feDistantLight", "feDropShadow", "feFlood", "feG<PERSON><PERSON><PERSON>lur", "feImage", "feMerge", "feMergeNode", "feMorphology", "feOffset", "fePointLight", "feSpecularLighting", "feSpotLight", "feTile", "feTurbulence", "foreignObject", "g", "image", "line", "linearGradient", "marker", "mask", "path", "pattern", "polygon", "polyline", "radialGradient", "rect", "stop", "svg", "symbol", "text", "textPath", "tspan", "use"], "sourceRoot": "../../src", "sources": ["xmlTags.ts"], "mappings": "AAAA,SACEA,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,aAAa,EACbC,mBAAmB,EACnBC,WAAW,EACXC,gBAAgB,EAChBC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc,EACdC,YAAY,EACZC,OAAO,EACPC,cAAc,EACdC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,QAAQ,EACRC,YAAY,EACZC,kBAAkB,EAClBC,WAAW,EACXC,MAAM,EACNC,YAAY,EACZC,MAAM,EACNC,aAAa,EACbC,CAAC,EACDC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRC,cAAc,EACdC,IAAI,EACJC,IAAI,EACJC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,GAAG,QACE,YAAY;AAEnB,OAAO,MAAMC,IAAI,GAAG;EAClBC,MAAM,EAAE/C,MAAM;EACdgD,QAAQ,EAAE/C,QAAQ;EAClBgD,IAAI,EAAE/C,IAAI;EACVgD,OAAO,EAAE/C,OAAO;EAChBgD,MAAM,EAAE1B,MAAM;EACd2B,OAAO,EAAEhD,OAAO;EAChBiD,aAAa,EAAEhD,aAAa;EAC5BiD,mBAAmB,EAAEhD,mBAAmB;EACxCiD,WAAW,EAAEhD,WAAW;EACxBiD,gBAAgB,EAAEhD,gBAAgB;EAClCiD,iBAAiB,EAAEhD,iBAAiB;EACpCiD,iBAAiB,EAAEhD,iBAAiB;EACpCiD,cAAc,EAAEhD,cAAc;EAC9BiD,YAAY,EAAEhD,YAAY;EAC1BiD,OAAO,EAAEhD,OAAO;EAChBiD,cAAc,EAAEhD,cAAc;EAC9BiD,OAAO,EAAEhD,OAAO;EAChBiD,OAAO,EAAEhD,OAAO;EAChBiD,WAAW,EAAEhD,WAAW;EACxBiD,YAAY,EAAEhD,YAAY;EAC1BiD,QAAQ,EAAEhD,QAAQ;EAClBiD,YAAY,EAAEhD,YAAY;EAC1BiD,kBAAkB,EAAEhD,kBAAkB;EACtCiD,WAAW,EAAEhD,WAAW;EACxBiD,MAAM,EAAEhD,MAAM;EACdiD,YAAY,EAAEhD,YAAY;EAC1BiD,aAAa,EAAE/C,aAAa;EAC5BgD,CAAC,EAAE/C,CAAC;EACJgD,KAAK,EAAE/C,KAAK;EACZgD,IAAI,EAAE/C,IAAI;EACVgD,cAAc,EAAE/C,cAAc;EAC9BgD,MAAM,EAAE/C,MAAM;EACdgD,IAAI,EAAE/C,IAAI;EACVgD,IAAI,EAAE/C,IAAI;EACVgD,OAAO,EAAE/C,OAAO;EAChBgD,OAAO,EAAE/C,OAAO;EAChBgD,QAAQ,EAAE/C,QAAQ;EAClBgD,cAAc,EAAE/C,cAAc;EAC9BgD,IAAI,EAAE/C,IAAI;EACVgD,IAAI,EAAE/C,IAAI;EACVgD,GAAG,EAAE/C,GAAG;EACRgD,MAAM,EAAE/C,MAAM;EACdgD,IAAI,EAAE/C,IAAI;EACVgD,QAAQ,EAAE/C,QAAQ;EAClBgD,KAAK,EAAE/C,KAAK;EACZgD,GAAG,EAAE/C;AACP,CAAU", "ignoreList": []}