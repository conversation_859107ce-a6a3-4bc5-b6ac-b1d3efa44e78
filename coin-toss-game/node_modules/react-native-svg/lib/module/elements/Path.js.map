{"version": 3, "names": ["React", "extract", "<PERSON><PERSON><PERSON>", "RNSVGPath", "Path", "displayName", "render", "props", "d", "pathProps", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Path.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,6BAA6B;AACrD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,+BAA+B;AASrD,eAAe,MAAMC,IAAI,SAASF,KAAK,CAAY;EACjD,OAAOG,WAAW,GAAG,MAAM;EAE3BC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MAAEC;IAAE,CAAC,GAAGD,KAAK;IACnB,MAAME,SAAS,GAAG;MAAE,GAAGR,OAAO,CAAC,IAAI,EAAEM,KAAK,CAAC;MAAEC;IAAE,CAAC;IAEhD,oBACER,KAAA,CAAAU,aAAA,CAACP,SAAS,EAAAQ,QAAA;MACRC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAoC;IAAE,GAC/DH,SAAS,CACd,CAAC;EAEN;AACF", "ignoreList": []}