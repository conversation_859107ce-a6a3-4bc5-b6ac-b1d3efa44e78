{"version": 3, "names": ["React", "extractTransform", "withoutXY", "extractText", "idPattern", "pickNotNil", "<PERSON><PERSON><PERSON>", "TSpan", "RNSVGTextPath", "TextPath", "displayName", "setNativeProps", "props", "matrix", "Object", "assign", "root", "render", "children", "xlinkHref", "href", "startOffset", "method", "spacing", "side", "alignmentBaseline", "midLine", "prop", "matched", "match", "ref", "refMethod", "createElement", "console", "warn"], "sourceRoot": "../../../src", "sources": ["elements/TextPath.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,SAAS,QAAQ,6BAA6B;AAWvD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,SAAS,EAAEC,UAAU,QAAQ,aAAa;AACnD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,mCAAmC;AAa7D,eAAe,MAAMC,QAAQ,SAASH,KAAK,CAAgB;EACzD,OAAOI,WAAW,GAAG,UAAU;EAE/BC,cAAc,GACZC,KAGkB,IACf;IACH,MAAMC,MAAM,GAAG,CAACD,KAAK,CAACC,MAAM,IAAIZ,gBAAgB,CAACW,KAAK,CAAC;IACvD,IAAIC,MAAM,EAAE;MACVD,KAAK,CAACC,MAAM,GAAGA,MAAM;IACvB;IACAC,MAAM,CAACC,MAAM,CAACH,KAAK,EAAEP,UAAU,CAACF,WAAW,CAACS,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACI,IAAI,IAAI,IAAI,CAACA,IAAI,CAACL,cAAc,CAACC,KAAK,CAAC;EAC9C,CAAC;EAEDK,MAAMA,CAAA,EAAG;IACP,MAAM;MACJC,QAAQ;MACRC,SAAS;MACTC,IAAI,GAAGD,SAAS;MAChBE,WAAW,GAAG,CAAC;MACfC,MAAM;MACNC,OAAO;MACPC,IAAI;MACJC,iBAAiB;MACjBC,OAAO;MACP,GAAGC;IACL,CAAC,GAAG,IAAI,CAACf,KAAK;IACd,MAAMgB,OAAO,GAAGR,IAAI,IAAIA,IAAI,CAACS,KAAK,CAACzB,SAAS,CAAC;IAC7C,MAAMyB,KAAK,GAAGD,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IACnC,IAAIC,KAAK,EAAE;MACT,MAAMjB,KAAK,GAAGV,SAAS,CAAC,IAAI,EAAEyB,IAAI,CAAC;MACnCb,MAAM,CAACC,MAAM,CACXH,KAAK,EACLT,WAAW,CACT;QACEe;MACF,CAAC,EACD,IACF,CAAC,EACD;QACEE,IAAI,EAAES,KAAK;QACXR,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,IAAI;QACJC,iBAAiB;QACjBC;MACF,CACF,CAAC;MACDd,KAAK,CAACkB,GAAG,GAAG,IAAI,CAACC,SAAiD;MAClE,oBAAO/B,KAAA,CAAAgC,aAAA,CAACxB,aAAa,EAAKI,KAAQ,CAAC;IACrC;IAEAqB,OAAO,CAACC,IAAI,CACV,oFAAoF,GAClFd,IAAI,GACJ,GACJ,CAAC;IACD,oBACEpB,KAAA,CAAAgC,aAAA,CAACzB,KAAK;MAACuB,GAAG,EAAE,IAAI,CAACC;IAAkD,GAChEb,QACI,CAAC;EAEZ;AACF", "ignoreList": []}