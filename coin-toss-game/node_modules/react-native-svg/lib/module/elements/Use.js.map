{"version": 3, "names": ["React", "withoutXY", "idPattern", "<PERSON><PERSON><PERSON>", "RNSVGUse", "Use", "displayName", "defaultProps", "x", "y", "width", "height", "render", "props", "children", "xlinkHref", "href", "matched", "match", "console", "warn", "useProps", "undefined", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Use.tsx"], "mappings": ";AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,6BAA6B;AAEvD,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,QAAQ,MAAM,8BAA8B;AAcnD,eAAe,MAAMC,GAAG,SAASF,KAAK,CAAW;EAC/C,OAAOG,WAAW,GAAG,KAAK;EAE1B,OAAOC,YAAY,GAAG;IACpBC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MACJC,QAAQ;MACRN,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNI,SAAS;MACTC,IAAI,GAAGD;IACT,CAAC,GAAGF,KAAK;IAET,MAAMI,OAAO,GAAGD,IAAI,IAAIA,IAAI,CAACE,KAAK,CAAChB,SAAS,CAAC;IAC7C,MAAMgB,KAAK,GAAGD,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IAEnC,IAAI,CAACC,KAAK,EAAE;MACVC,OAAO,CAACC,IAAI,CACV,+EAA+E,GAC7EJ,IAAI,GACJ,GACJ,CAAC;IACH;IACA,MAAMK,QAAQ,GAAG;MACfL,IAAI,EAAEE,KAAK,IAAII,SAAS;MACxBd,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC;IACF,CAAC;IACD,oBACEX,KAAA,CAAAuB,aAAA,CAACnB,QAAQ,EAAAoB,QAAA;MACPC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAmC;IAAE,GAC9DxB,SAAS,CAAC,IAAI,EAAEY,KAAK,CAAC,EACtBQ,QAAQ,GACXP,QACO,CAAC;EAEf;AACF", "ignoreList": []}