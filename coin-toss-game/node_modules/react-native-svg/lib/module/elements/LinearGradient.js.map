{"version": 3, "names": ["React", "extractGradient", "<PERSON><PERSON><PERSON>", "RNSVGLinearGradient", "LinearGradient", "displayName", "defaultProps", "x1", "y1", "x2", "y2", "render", "props", "linearGradientProps", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/LinearGradient.tsx"], "mappings": ";AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,gCAAgC;AAE5D,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,mBAAmB,MAAM,yCAAyC;AAczE,eAAe,MAAMC,cAAc,SAASF,KAAK,CAAsB;EACrE,OAAOG,WAAW,GAAG,gBAAgB;EAErC,OAAOC,YAAY,GAAG;IACpBC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE;EACN,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MAAEL,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC;IAAG,CAAC,GAAGE,KAAK;IAChC,MAAMC,mBAAmB,GAAG;MAAEN,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC;IAAG,CAAC;IAC9C,oBACEV,KAAA,CAAAc,aAAA,CAACX,mBAAmB,EAAAY,QAAA;MAClBC,GAAG,EAAGA,GAAG,IACP,IAAI,CAACC,SAAS,CAACD,GAA8C;IAC9D,GACGH,mBAAmB,EACnBZ,eAAe,CAACW,KAAK,EAAE,IAAI,CAAC,CACjC,CAAC;EAEN;AACF", "ignoreList": []}