{"version": 3, "names": ["React", "useState", "useEffect", "Component", "Image", "Platform", "fetchText", "resolveAssetUri", "SvgCss", "SvgWithCss", "getUriFromSource", "source", "resolvedAssetSource", "OS", "resolveAssetSource", "uri", "loadLocalRawResourceDefault", "isUriAnAndroidResourceIdentifier", "indexOf", "loadAndroidRawResource", "RNSVGRenderableModule", "require", "default", "getRawResource", "e", "console", "error", "loadLocalRawResourceAndroid", "loadLocalRawResource", "LocalSvg", "props", "asset", "rest", "xml", "setXml", "then", "createElement", "_extends", "WithLocalSvg", "state", "componentDidMount", "load", "componentDidUpdate", "prevProps", "setState", "render", "override"], "sourceRoot": "../../../src", "sources": ["css/LocalSvg.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,QAAQ,OAAO;AACtD,SAASC,KAAK,EAAEC,QAAQ,QAAkC,cAAc;AACxE,SAASC,SAAS,QAAuB,kBAAkB;AAC3D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AAE1C,OAAO,SAASC,gBAAgBA,CAACC,MAA2B,EAAE;EAC5D,MAAMC,mBAAmB,GACvBP,QAAQ,CAACQ,EAAE,KAAK,KAAK,GACjBN,eAAe,CAACI,MAAM,CAAC,GACvBP,KAAK,CAACU,kBAAkB,CAACH,MAAM,CAAC;EACtC,OAAOC,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEG,GAAG;AACjC;AAEA,OAAO,SAASC,2BAA2BA,CAACL,MAA2B,EAAE;EACvE,MAAMI,GAAG,GAAGL,gBAAgB,CAACC,MAAM,CAAC;EACpC,OAAOL,SAAS,CAACS,GAAG,CAAC;AACvB;AAEA,OAAO,SAASE,gCAAgCA,CAACF,GAAY,EAAE;EAC7D,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1D;AAEA,OAAO,eAAeC,sBAAsBA,CAACJ,GAAW,EAAE;EACxD,IAAI;IACF;IACA,MAAMK,qBAA0B;IAC9B;IACA;IACAC,OAAO,CAAC,qCAAqC,CAAC,CAACC,OAAO;IACxD,OAAO,MAAMF,qBAAqB,CAACG,cAAc,CAACR,GAAG,CAAC;EACxD,CAAC,CAAC,OAAOS,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CACX,mFAAmF,EACnFF,CACF,CAAC;IACD,OAAO,IAAI;EACb;AACF;AAEA,OAAO,SAASG,2BAA2BA,CAAChB,MAA2B,EAAE;EACvE,MAAMI,GAAG,GAAGL,gBAAgB,CAACC,MAAM,CAAC;EACpC,IAAII,GAAG,IAAIE,gCAAgC,CAACF,GAAG,CAAC,EAAE;IAChD,OAAOI,sBAAsB,CAACJ,GAAG,CAAC;EACpC,CAAC,MAAM;IACL,OAAOT,SAAS,CAACS,GAAG,CAAC;EACvB;AACF;AAEA,OAAO,MAAMa,oBAAoB,GAC/BvB,QAAQ,CAACQ,EAAE,KAAK,SAAS,GACrBG,2BAA2B,GAC3BW,2BAA2B;AAQjC,OAAO,SAASE,QAAQA,CAACC,KAAiB,EAAE;EAC1C,MAAM;IAAEC,KAAK;IAAE,GAAGC;EAAK,CAAC,GAAGF,KAAK;EAChC,MAAM,CAACG,GAAG,EAAEC,MAAM,CAAC,GAAGjC,QAAQ,CAAgB,IAAI,CAAC;EACnDC,SAAS,CAAC,MAAM;IACd0B,oBAAoB,CAACG,KAAK,CAAC,CAACI,IAAI,CAACD,MAAM,CAAC;EAC1C,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;EACX,oBAAO/B,KAAA,CAAAoC,aAAA,CAAC5B,MAAM,EAAA6B,QAAA;IAACJ,GAAG,EAAEA;EAAI,GAAKD,IAAI,CAAG,CAAC;AACvC;AAEA,OAAO,MAAMM,YAAY,SAASnC,SAAS,CAAyB;EAClEoC,KAAK,GAAG;IAAEN,GAAG,EAAE;EAAK,CAAC;EACrBO,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,IAAI,CAAC,IAAI,CAACX,KAAK,CAACC,KAAK,CAAC;EAC7B;EAEAW,kBAAkBA,CAACC,SAAyC,EAAE;IAC5D,MAAM;MAAEZ;IAAM,CAAC,GAAG,IAAI,CAACD,KAAK;IAC5B,IAAIC,KAAK,KAAKY,SAAS,CAACZ,KAAK,EAAE;MAC7B,IAAI,CAACU,IAAI,CAACV,KAAK,CAAC;IAClB;EACF;EAEA,MAAMU,IAAIA,CAACV,KAA0B,EAAE;IACrC,IAAI;MACF,IAAI,CAACa,QAAQ,CAAC;QAAEX,GAAG,EAAEF,KAAK,GAAG,MAAMH,oBAAoB,CAACG,KAAK,CAAC,GAAG;MAAK,CAAC,CAAC;IAC1E,CAAC,CAAC,OAAOP,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;IAClB;EACF;EAEAqB,MAAMA,CAAA,EAAG;IACP,MAAM;MACJf,KAAK;MACLS,KAAK,EAAE;QAAEN;MAAI;IACf,CAAC,GAAG,IAAI;IACR,oBAAOjC,KAAA,CAAAoC,aAAA,CAAC3B,UAAU;MAACwB,GAAG,EAAEA,GAAI;MAACa,QAAQ,EAAEhB;IAAM,CAAE,CAAC;EAClD;AACF;AAEA,eAAeD,QAAQ", "ignoreList": []}