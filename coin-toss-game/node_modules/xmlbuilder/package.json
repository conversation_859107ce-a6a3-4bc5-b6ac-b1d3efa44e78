{"name": "xmlbuilder", "version": "15.1.1", "keywords": ["xml", "xmlbuilder"], "homepage": "http://github.com/oozcitak/xmlbuilder-js", "description": "An XML builder for node.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/oozcitak/xmlbuilder-js.git"}, "bugs": {"url": "http://github.com/oozcitak/xmlbuilder-js/issues"}, "main": "./lib/index", "typings": "./typings/index.d.ts", "engines": {"node": ">=8.0"}, "dependencies": {}, "devDependencies": {"coffee-coverage": "*", "coffeescript": "2.4.1", "coveralls": "*", "istanbul": "*", "mocha": "*", "nyc": "*", "xpath": "*", "git-state": "*"}, "mocha": {"require": ["coffeescript/register", "coffee-coverage/register-istanbul", "test/common.coffee"], "recursive": true, "ui": "tdd", "reporter": "dot"}, "scripts": {"prepublishOnly": "coffee -co lib src", "test": "nyc mocha \"test/**/*.coffee\"", "perf": "coffee ./perf/index.coffee"}}