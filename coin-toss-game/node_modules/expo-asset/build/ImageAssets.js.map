{"version": 3, "file": "ImageAssets.js", "sourceRoot": "", "sources": ["../src/ImageAssets.ts"], "names": [], "mappings": "AAAA,wBAAwB;AACxB,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAQ1C,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,OAAO,qCAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1D,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,GAAW;IAC3C,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1E,CAAC;IACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;QACxB,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC;QACrB,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;YAChB,OAAO,CAAC;gBACN,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC;gBACtB,KAAK,EAAE,GAAG,CAAC,YAAY;gBACvB,MAAM,EAAE,GAAG,CAAC,aAAa;aAC1B,CAAC,CAAC;QACL,CAAC,CAAC;QACF,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;IAChB,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/* eslint-env browser */\nimport { getFilename } from './AssetUris';\n\ntype ImageInfo = {\n  name: string;\n  width: number;\n  height: number;\n};\n\nexport function isImageType(type: string): boolean {\n  return /^(jpeg|jpg|gif|png|bmp|webp|heic)$/i.test(type);\n}\n\nexport function getImageInfoAsync(url: string): Promise<ImageInfo> {\n  if (typeof window === 'undefined') {\n    return Promise.resolve({ name: getFilename(url), width: 0, height: 0 });\n  }\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onerror = reject;\n    img.onload = () => {\n      resolve({\n        name: getFilename(url),\n        width: img.naturalWidth,\n        height: img.naturalHeight,\n      });\n    };\n    img.src = url;\n  });\n}\n"]}