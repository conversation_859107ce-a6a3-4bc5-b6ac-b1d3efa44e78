{"version": 3, "file": "resolveAssetSource.native.js", "sourceRoot": "", "sources": ["../src/resolveAssetSource.native.ts"], "names": [], "mappings": "AAAA,OAAO,kBAAkB,MAAM,iDAAiD,CAAC;AACjF,eAAe,kBAAkB,CAAC;AAClC,cAAc,iDAAiD,CAAC", "sourcesContent": ["import resolveAssetSource from 'react-native/Libraries/Image/resolveAssetSource';\nexport default resolveAssetSource;\nexport * from 'react-native/Libraries/Image/resolveAssetSource';\n"]}